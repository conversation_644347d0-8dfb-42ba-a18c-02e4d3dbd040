# 阶段二：核心功能整合实施计划（第3-4周）

## 1. 阶段概览

### 1.1 目标和范围
- **主要目标**: 整合参数收集管理系统和流式处理系统
- **技术重点**: 参数管理器、流式处理、WebSocket集成
- **时间周期**: 2周（14个工作日）
- **关键交付物**: 增强参数管理系统 + 完整流式处理系统

### 1.2 当前系统分析
基于阶段一完成的基础整合，现有关键组件：

**已完成的基础组件**:
- `app/services/ai_assistant/integration/state_adapter.py` - 统一状态适配器
- `app/services/ai_assistant/integration/state_manager.py` - 整合状态管理器
- `app/services/ai_assistant/integration/intent_processor.py` - 整合意图处理器
- `app/services/ai_assistant/integration/interfaces.py` - 核心接口定义

**需要整合的核心模块**:
- `app/services/ai_assistant/parameter/extractor.py` - 参数提取器
- `app/services/ai_assistant/parameter/validators.py` - 参数验证器
- `app/services/ai_assistant/langgraph/enhanced_exercise_graph.py` - LangGraph图实现
- `app/services/ai_assistant/langgraph/nodes/` - 各类处理节点
- `app/services/ai_assistant/conversation/orchestrator.py` - 会话编排器

**现有流式处理组件**:
- `app/services/ai_assistant/langgraph/test_api_integration.py` - API集成测试
- `app/services/ai_assistant/langgraph/nodes/response_generation.py` - 响应生成节点
- WebSocket相关的现有实现

### 1.3 技术架构
```
app/services/ai_assistant/integration/
├── parameter_manager.py          # 新建：增强参数管理器
├── enhanced_langgraph_service.py # 新建：增强LangGraph服务
├── streaming_manager.py          # 新建：流式处理管理器
└── websocket_handler.py          # 新建：WebSocket处理器
```

## 2. 第3周：参数收集系统整合

### 2.1 Day 15-17: 参数管理器增强

#### 2.1.1 文件创建清单
- [ ] `app/services/ai_assistant/integration/parameter_manager.py` - 增强参数管理器
- [ ] `app/services/ai_assistant/integration/parameter_validator.py` - 参数验证器
- [ ] `tests/integration/test_parameter_manager.py` - 参数管理器测试

#### 2.1.2 现有组件分析
基于现有的参数提取和收集系统：

**现有参数提取器**:
- `app/services/parameter_extractor.py` - 基础参数提取器
- `app/services/enhanced_parameter_extractor.py` - 增强参数提取器
- `app/services/graph_nodes/param_collector_node.py` - 参数收集节点

**现有参数管理器**:
- `app/services/conversation/training_param_manager.py` - 训练参数管理器
- `app/services/conversation/user_profile_manager.py` - 用户信息管理器

#### 2.1.3 核心实现 - parameter_manager.py
基于现有组件进行整合和增强：
```python
import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .interfaces import ParameterManagerInterface
from .state_adapter import IntegratedStateAdapter
from ..langgraph.state_definitions import UnifiedFitnessState

# 导入现有的参数提取器
from ...parameter_extractor import ParameterExtractor
from ...enhanced_parameter_extractor import EnhancedParameterExtractor
from ...conversation.training_param_manager import TrainingParamManager
from ...conversation.user_profile_manager import UserProfileManager

# 导入现有的图节点
from ...graph_nodes.param_collector_node import param_collector_node
from ...graph_nodes.user_info_collector_node import user_info_collector_node

from ...logger.logger import get_logger

logger = get_logger(__name__)

class ParameterValidator:
    """参数验证器"""
    
    def __init__(self):
        self.validation_rules = self._init_validation_rules()
    
    def _init_validation_rules(self) -> Dict[str, Dict]:
        """初始化验证规则"""
        return {
            "user_profile": {
                "name": {"type": str, "required": True, "max_length": 50},
                "age": {"type": int, "required": True, "min": 12, "max": 100},
                "gender": {"type": str, "required": True, "choices": ["male", "female"]},
                "height": {"type": (int, float), "required": True, "min": 100, "max": 250},
                "weight": {"type": (int, float), "required": True, "min": 30, "max": 300},
                "fitness_level": {"type": str, "required": False, "choices": ["beginner", "intermediate", "advanced"]}
            },
            "training_params": {
                "goal": {"type": str, "required": True, "choices": ["weight_loss", "muscle_gain", "endurance", "strength"]},
                "duration": {"type": int, "required": True, "min": 15, "max": 180},
                "frequency": {"type": int, "required": True, "min": 1, "max": 7},
                "intensity": {"type": str, "required": False, "choices": ["low", "moderate", "high"]},
                "equipment": {"type": list, "required": False}
            }
        }
    
    def validate_user_profile(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """验证用户档案"""
        return self._validate_params(profile, "user_profile")
    
    def validate_training_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证训练参数"""
        return self._validate_params(params, "training_params")
    
    def _validate_params(self, params: Dict[str, Any], param_type: str) -> Dict[str, Any]:
        """通用参数验证"""
        rules = self.validation_rules.get(param_type, {})
        validated_params = {}
        errors = []
        
        for field, rule in rules.items():
            value = params.get(field)
            
            # 检查必需字段
            if rule.get("required", False) and (value is None or value == ""):
                errors.append(f"{field}为必需字段")
                continue
            
            if value is not None:
                # 类型检查
                expected_type = rule.get("type")
                if expected_type and not isinstance(value, expected_type):
                    try:
                        # 尝试类型转换
                        if expected_type == int:
                            value = int(value)
                        elif expected_type == float:
                            value = float(value)
                        elif expected_type == str:
                            value = str(value)
                    except (ValueError, TypeError):
                        errors.append(f"{field}类型错误，期望{expected_type}")
                        continue
                
                # 范围检查
                if "min" in rule and value < rule["min"]:
                    errors.append(f"{field}不能小于{rule['min']}")
                    continue
                if "max" in rule and value > rule["max"]:
                    errors.append(f"{field}不能大于{rule['max']}")
                    continue
                
                # 选择值检查
                if "choices" in rule and value not in rule["choices"]:
                    errors.append(f"{field}必须为{rule['choices']}中的一个")
                    continue
                
                # 长度检查
                if "max_length" in rule and len(str(value)) > rule["max_length"]:
                    errors.append(f"{field}长度不能超过{rule['max_length']}")
                    continue
                
                validated_params[field] = value
        
        if errors:
            raise ValueError(f"参数验证失败: {'; '.join(errors)}")
        
        return validated_params

class EnhancedParameterManager(ParameterManagerInterface):
    """增强参数管理器 - 基于现有参数提取器，整合参数管理逻辑"""

    def __init__(self, db_session, llm_service):
        # 数据库会话和服务
        self.db_session = db_session
        self.llm_service = llm_service

        # 使用现有的参数提取器
        self.parameter_extractor = ParameterExtractor(llm_service)
        self.enhanced_extractor = EnhancedParameterExtractor()

        # 使用现有的管理器
        self.training_param_manager = TrainingParamManager(db_session)
        self.user_profile_manager = UserProfileManager(db_session)

        # 参数验证器
        self.parameter_validator = ParameterValidator()

        # 配置参数 - 基于现有系统配置
        self.max_collection_rounds = 5  # 最大收集轮数
        self.collection_timeout = 300   # 收集超时时间（秒）

        # 必需字段定义 - 基于现有系统的字段要求
        self.required_user_fields = ["age", "gender", "height", "weight", "fitness_goal"]
        self.required_training_fields = {
            "training_plan": ["body_part", "scenario", "plan_type"],
            "exercise_recommendation": ["body_part", "scenario"],
            "fitness_advice": ["goal"],
            "daily_workout_plan": ["body_part", "scenario"],
            "weekly_workout_plan": ["body_part", "scenario", "plan_type"]
        }

        # 状态适配器
        self.state_adapter = IntegratedStateAdapter()

        logger.info("增强参数管理器初始化完成")

    def _create_user_info_extractor(self) -> LLMParameterExtractor:
        """创建用户信息提取器"""
        user_info_schema = {
            "name": {
                "type": "string",
                "description": "用户姓名",
                "required": True
            },
            "age": {
                "type": "integer",
                "description": "用户年龄",
                "required": True
            },
            "gender": {
                "type": "string",
                "description": "用户性别（male/female）",
                "required": True
            },
            "height": {
                "type": "number",
                "description": "用户身高（厘米）",
                "required": True
            },
            "weight": {
                "type": "number",
                "description": "用户体重（公斤）",
                "required": True
            },
            "fitness_level": {
                "type": "string",
                "description": "健身水平（beginner/intermediate/advanced）",
                "required": False
            }
        }

        return LLMParameterExtractor(
            parameter_schema=user_info_schema,
            model="intent-recognition-app"
        )

    def _create_training_params_extractor(self) -> LLMParameterExtractor:
        """创建训练参数提取器"""
        training_params_schema = {
            "goal": {
                "type": "string",
                "description": "健身目标（weight_loss/muscle_gain/endurance/strength）",
                "required": True
            },
            "duration": {
                "type": "integer",
                "description": "单次训练时长（分钟）",
                "required": True
            },
            "frequency": {
                "type": "integer",
                "description": "每周训练频率（次数）",
                "required": True
            },
            "intensity": {
                "type": "string",
                "description": "训练强度（low/moderate/high）",
                "required": False
            },
            "equipment": {
                "type": "array",
                "description": "可用器械列表",
                "required": False
            }
        }

        return LLMParameterExtractor(
            parameter_schema=training_params_schema,
            model="intent-recognition-app"
        )

    async def collect_user_info(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """收集用户信息 - 使用原始系统的成熟逻辑"""
        try:
            logger.info(f"开始收集用户信息: {state['conversation_id']}")
            
            # 检查是否已在收集流程中
            flow_state = state.get("flow_state", {})
            if flow_state.get("collecting_user_info"):
                return await self._continue_user_info_collection(state)
            
            # 检查用户信息完整性
            missing_fields = await self.user_profile_manager.check_missing_fields(
                state["user_profile"]
            )
            
            if missing_fields:
                # 开始用户信息收集流程
                return await self._start_user_info_collection(state, missing_fields)
            else:
                # 用户信息完整，继续处理
                state["flow_state"]["user_info_complete"] = True
                state["response_content"] = "用户信息已完整，可以继续为您制定健身计划。"
                logger.info(f"用户信息已完整: {state['conversation_id']}")
            
            return state
            
        except Exception as e:
            logger.error(f"用户信息收集失败: {str(e)}")
            return await self._fallback_user_info_collection(state)
    
    async def collect_training_params(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """收集训练参数 - 使用原始系统的智能提取"""
        try:
            logger.info(f"开始收集训练参数: {state['conversation_id']}")
            
            # 检查是否已在收集流程中
            flow_state = state.get("flow_state", {})
            if flow_state.get("collecting_training_params"):
                return await self._continue_training_params_collection(state)
            
            # 从消息中提取参数
            latest_message = state["messages"][-1].content if state["messages"] else ""
            extracted_params = await self.parameter_extractor.extract_training_params(
                latest_message, state["training_params"]
            )
            
            # 验证和标准化参数
            try:
                validated_params = self.parameter_validator.validate_training_params(
                    extracted_params
                )
                # 更新状态
                state["training_params"].update(validated_params)
                logger.debug(f"参数验证成功: {validated_params}")
            except ValueError as e:
                logger.warning(f"参数验证失败: {str(e)}")
                # 保留原始参数，但记录验证错误
                state["training_params"].update(extracted_params)
                state["flow_state"]["param_validation_errors"] = str(e)
            
            # 检查参数完整性
            missing_params = await self.training_param_manager.check_missing_params(
                state["training_params"], state["intent"]
            )
            
            if missing_params:
                # 开始训练参数收集流程
                return await self._start_training_params_collection(state, missing_params)
            else:
                # 参数收集完成
                state["flow_state"]["training_params_complete"] = True
                state["response_content"] = "训练参数收集完成，正在为您制定个性化健身计划..."
                logger.info(f"训练参数收集完成: {state['conversation_id']}")
            
            return state
            
        except Exception as e:
            logger.error(f"训练参数收集失败: {str(e)}")
            return await self._fallback_param_collection(state)
    
    async def _start_user_info_collection(self, state: UnifiedFitnessState, missing_fields: List[str]) -> UnifiedFitnessState:
        """开始用户信息收集流程"""
        try:
            # 设置收集状态
            state["flow_state"]["collecting_user_info"] = True
            state["flow_state"]["missing_fields"] = missing_fields
            state["flow_state"]["current_field"] = missing_fields[0]
            state["flow_state"]["collection_start_time"] = time.time()
            state["flow_state"]["collection_round"] = 1
            
            # 生成收集提示
            collection_prompt = await self.user_profile_manager.generate_collection_prompt(
                missing_fields[0], state["user_profile"]
            )
            
            state["response_content"] = collection_prompt
            state["response_type"] = "user_info_collection"
            
            logger.info(f"开始收集用户信息: {missing_fields[0]}")
            return state
            
        except Exception as e:
            logger.error(f"开始用户信息收集失败: {str(e)}")
            raise
    
    async def _continue_user_info_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """继续用户信息收集流程"""
        try:
            flow_state = state["flow_state"]
            current_field = flow_state.get("current_field")
            missing_fields = flow_state.get("missing_fields", [])
            
            # 检查超时
            collection_start_time = flow_state.get("collection_start_time", time.time())
            if time.time() - collection_start_time > self.collection_timeout:
                logger.warning("用户信息收集超时")
                return await self._timeout_user_info_collection(state)
            
            # 从最新消息中提取用户信息
            latest_message = state["messages"][-1].content if state["messages"] else ""
            extracted_info = await self.user_profile_manager.extract_user_info(
                latest_message, current_field, state["user_profile"]
            )
            
            if extracted_info:
                # 验证提取的信息
                try:
                    validated_info = self.parameter_validator.validate_user_profile(
                        {current_field: extracted_info}
                    )
                    state["user_profile"].update(validated_info)
                    logger.info(f"成功收集用户信息: {current_field} = {extracted_info}")
                    
                    # 移除已收集的字段
                    if current_field in missing_fields:
                        missing_fields.remove(current_field)
                    
                    # 检查是否还有缺失字段
                    if missing_fields:
                        # 继续收集下一个字段
                        next_field = missing_fields[0]
                        flow_state["current_field"] = next_field
                        flow_state["collection_round"] += 1
                        
                        collection_prompt = await self.user_profile_manager.generate_collection_prompt(
                            next_field, state["user_profile"]
                        )
                        state["response_content"] = collection_prompt
                        
                        logger.info(f"继续收集用户信息: {next_field}")
                    else:
                        # 收集完成
                        flow_state["collecting_user_info"] = False
                        flow_state["user_info_complete"] = True
                        state["response_content"] = "用户信息收集完成！现在为您制定个性化健身计划。"
                        logger.info("用户信息收集完成")
                
                except ValueError as e:
                    # 验证失败，重新收集
                    logger.warning(f"用户信息验证失败: {str(e)}")
                    retry_prompt = await self.user_profile_manager.generate_retry_prompt(
                        current_field, str(e)
                    )
                    state["response_content"] = retry_prompt
                    flow_state["collection_round"] += 1
            
            else:
                # 未能提取到有效信息，重新询问
                flow_state["collection_round"] += 1
                if flow_state["collection_round"] > self.max_collection_rounds:
                    logger.warning("用户信息收集达到最大轮数")
                    return await self._timeout_user_info_collection(state)
                
                retry_prompt = await self.user_profile_manager.generate_retry_prompt(
                    current_field, "未能识别您的回答"
                )
                state["response_content"] = retry_prompt
            
            return state
            
        except Exception as e:
            logger.error(f"继续用户信息收集失败: {str(e)}")
            return await self._fallback_user_info_collection(state)
    
    async def _start_training_params_collection(self, state: UnifiedFitnessState, missing_params: List[str]) -> UnifiedFitnessState:
        """开始训练参数收集流程"""
        try:
            # 设置收集状态
            state["flow_state"]["collecting_training_params"] = True
            state["flow_state"]["missing_params"] = missing_params
            state["flow_state"]["current_param"] = missing_params[0]
            state["flow_state"]["param_collection_start_time"] = time.time()
            state["flow_state"]["param_collection_round"] = 1
            
            # 生成参数收集提示
            param_prompt = await self.training_param_manager.generate_param_prompt(
                missing_params[0], state["training_params"]
            )
            
            state["response_content"] = param_prompt
            state["response_type"] = "training_params_collection"
            
            logger.info(f"开始收集训练参数: {missing_params[0]}")
            return state
            
        except Exception as e:
            logger.error(f"开始训练参数收集失败: {str(e)}")
            raise
    
    async def _continue_training_params_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """继续训练参数收集流程"""
        try:
            flow_state = state["flow_state"]
            current_param = flow_state.get("current_param")
            missing_params = flow_state.get("missing_params", [])
            
            # 检查超时
            collection_start_time = flow_state.get("param_collection_start_time", time.time())
            if time.time() - collection_start_time > self.collection_timeout:
                logger.warning("训练参数收集超时")
                return await self._timeout_training_params_collection(state)
            
            # 从最新消息中提取训练参数
            latest_message = state["messages"][-1].content if state["messages"] else ""
            extracted_param = await self.training_param_manager.extract_training_param(
                latest_message, current_param, state["training_params"]
            )
            
            if extracted_param:
                # 验证提取的参数
                try:
                    validated_param = self.parameter_validator.validate_training_params(
                        {current_param: extracted_param}
                    )
                    state["training_params"].update(validated_param)
                    logger.info(f"成功收集训练参数: {current_param} = {extracted_param}")
                    
                    # 移除已收集的参数
                    if current_param in missing_params:
                        missing_params.remove(current_param)
                    
                    # 检查是否还有缺失参数
                    if missing_params:
                        # 继续收集下一个参数
                        next_param = missing_params[0]
                        flow_state["current_param"] = next_param
                        flow_state["param_collection_round"] += 1
                        
                        param_prompt = await self.training_param_manager.generate_param_prompt(
                            next_param, state["training_params"]
                        )
                        state["response_content"] = param_prompt
                        
                        logger.info(f"继续收集训练参数: {next_param}")
                    else:
                        # 收集完成
                        flow_state["collecting_training_params"] = False
                        flow_state["training_params_complete"] = True
                        state["response_content"] = "训练参数收集完成！正在为您生成个性化健身计划..."
                        logger.info("训练参数收集完成")
                
                except ValueError as e:
                    # 验证失败，重新收集
                    logger.warning(f"训练参数验证失败: {str(e)}")
                    retry_prompt = await self.training_param_manager.generate_retry_prompt(
                        current_param, str(e)
                    )
                    state["response_content"] = retry_prompt
                    flow_state["param_collection_round"] += 1
            
            else:
                # 未能提取到有效参数，重新询问
                flow_state["param_collection_round"] += 1
                if flow_state["param_collection_round"] > self.max_collection_rounds:
                    logger.warning("训练参数收集达到最大轮数")
                    return await self._timeout_training_params_collection(state)
                
                retry_prompt = await self.training_param_manager.generate_retry_prompt(
                    current_param, "未能识别您的回答"
                )
                state["response_content"] = retry_prompt
            
            return state
            
        except Exception as e:
            logger.error(f"继续训练参数收集失败: {str(e)}")
            return await self._fallback_param_collection(state)
    
    async def _fallback_user_info_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """用户信息收集降级处理"""
        state["flow_state"]["collecting_user_info"] = False
        state["flow_state"]["user_info_collection_failed"] = True
        state["response_content"] = "用户信息收集遇到问题，将使用默认设置为您制定健身计划。您可以稍后在个人资料中完善信息。"
        return state
    
    async def _fallback_param_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """训练参数收集降级处理"""
        state["flow_state"]["collecting_training_params"] = False
        state["flow_state"]["param_collection_failed"] = True
        state["response_content"] = "训练参数收集遇到问题，将为您推荐基础健身计划。您可以稍后调整训练设置。"
        return state

#### 2.1.3 验收标准
- [ ] 参数收集流程完整性 100%
- [ ] 参数验证准确率 >95%
- [ ] 收集超时和错误处理正常
- [ ] 用户信息和训练参数分别收集成功

### 2.2 Day 18-21: 参数收集流程测试

#### 2.2.1 测试清单
- [ ] 用户信息收集流程测试
- [ ] 训练参数提取准确性测试
- [ ] 参数验证逻辑测试
- [ ] 收集流程中断恢复测试
- [ ] 并发参数收集测试

## 3. 第4周：流式处理系统整合

### 3.1 Day 22-24: LangGraph服务增强

#### 3.1.1 文件创建清单
- [ ] `app/services/ai_assistant/integration/enhanced_langgraph_service.py`
- [ ] `app/services/ai_assistant/integration/streaming_manager.py`
- [ ] `app/services/ai_assistant/integration/websocket_handler.py`

#### 3.1.2 核心实现 - enhanced_langgraph_service.py
```python
import asyncio
import json
import time
from typing import Dict, Any, AsyncGenerator, Optional
from contextlib import asynccontextmanager

from .state_adapter import UnifiedStateAdapter
from .state_manager import IntegratedStateManager
from .intent_processor import IntegratedIntentProcessor
from .parameter_manager import EnhancedParameterManager
from ..langgraph.state_definitions import UnifiedFitnessState
from ..langgraph.langgraph_service import LangGraphService
from ...logger.logger import get_logger

logger = get_logger(__name__)

class EnhancedLangGraphService:
    """增强LangGraph服务 - 整合流式处理和统一状态管理"""
    
    def __init__(self, db_session, redis_client, llm_service):
        # 核心组件
        self.state_manager = IntegratedStateManager(db_session, redis_client)
        self.intent_processor = IntegratedIntentProcessor(llm_service, db_session)
        self.parameter_manager = EnhancedParameterManager(db_session, llm_service)
        
        # 原有LangGraph服务
        self.original_langgraph = LangGraphService(db_session, redis_client)
        
        # 流式处理配置
        self.enable_streaming = True
        self.chunk_size = 50  # 流式传输块大小
        self.stream_delay = 0.05  # 流式传输延迟（秒）
        
        # 性能监控
        self.performance_metrics = {}
    
    async def process_message_stream(self, 
                                   message: str, 
                                   conversation_id: str,
                                   user_id: str = None,
                                   enable_streaming: bool = True) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理消息 - 主要接口"""
        
        processing_start_time = time.time()
        
        try:
            logger.info(f"开始流式处理: {conversation_id}")
            
            # 1. 获取或创建统一状态
            state = await self.state_manager.get_current_state(conversation_id)
            if user_id:
                state["user_id"] = user_id
            
            # 添加新消息到状态
            from langchain_core.messages import HumanMessage
            state["messages"].append(HumanMessage(content=message))
            state["processing_start_time"] = processing_start_time
            
            # 2. 流式处理开始通知
            yield {
                "type": "processing_start",
                "conversation_id": conversation_id,
                "timestamp": time.time(),
                "message": "开始处理您的请求..."
            }
            
            # 3. 意图处理
            yield {
                "type": "intent_processing",
                "message": "正在分析您的意图..."
            }
            
            state = await self.intent_processor.process_intent(message, state)
            
            yield {
                "type": "intent_recognized",
                "intent": state["intent"],
                "confidence": state["confidence"],
                "message": f"已识别意图: {state['intent']}"
            }
            
            # 4. 参数收集（如需要）
            if await self._needs_parameter_collection(state):
                yield {
                    "type": "parameter_collection_start",
                    "message": "正在收集必要参数..."
                }
                
                # 用户信息收集
                if await self._needs_user_info_collection(state):
                    state = await self.parameter_manager.collect_user_info(state)
                    
                    if state["flow_state"].get("collecting_user_info"):
                        # 正在收集用户信息
                        yield {
                            "type": "user_info_collection",
                            "message": state["response_content"],
                            "collecting_field": state["flow_state"].get("current_field"),
                            "response_type": "user_info_collection"
                        }
                        
                        # 保存状态并结束
                        await self.state_manager.save_state(state)
                        yield {
                            "type": "processing_complete",
                            "conversation_id": conversation_id,
                            "processing_time": time.time() - processing_start_time
                        }
                        return
                
                # 训练参数收集
                if await self._needs_training_params_collection(state):
                    state = await self.parameter_manager.collect_training_params(state)
                    
                    if state["flow_state"].get("collecting_training_params"):
                        # 正在收集训练参数
                        yield {
                            "type": "training_params_collection", 
                            "message": state["response_content"],
                            "collecting_param": state["flow_state"].get("current_param"),
                            "response_type": "training_params_collection"
                        }
                        
                        # 保存状态并结束
                        await self.state_manager.save_state(state)
                        yield {
                            "type": "processing_complete",
                            "conversation_id": conversation_id,
                            "processing_time": time.time() - processing_start_time
                        }
                        return
            
            # 5. 核心业务处理
            yield {
                "type": "core_processing",
                "message": "正在处理您的请求..."
            }
            
            # 根据意图调用对应的处理逻辑
            if state["intent"] in ["create_training_plan", "modify_training_plan"]:
                async for chunk in self._process_training_plan_stream(state):
                    yield chunk
            elif state["intent"] in ["exercise_recommendation", "exercise_search"]:
                async for chunk in self._process_exercise_recommendation_stream(state):
                    yield chunk
            elif state["intent"] in ["fitness_qa", "nutrition_qa"]:
                async for chunk in self._process_qa_stream(state):
                    yield chunk
            else:
                async for chunk in self._process_general_chat_stream(state):
                    yield chunk
            
            # 6. 保存最终状态
            await self.state_manager.save_state(state)
            
            # 7. 处理完成通知
            processing_time = time.time() - processing_start_time
            yield {
                "type": "processing_complete",
                "conversation_id": conversation_id,
                "processing_time": processing_time,
                "message": "处理完成"
            }
            
            # 8. 更新性能指标
            await self._update_performance_metrics(conversation_id, processing_time, state)
            
        except Exception as e:
            logger.error(f"流式处理失败: {str(e)}")
            yield {
                "type": "error",
                "error_message": "处理过程中出现错误，请稍后重试",
                "conversation_id": conversation_id,
                "error_details": str(e) if logger.level <= 10 else None  # DEBUG级别才显示详细错误
            }
    
    async def _process_training_plan_stream(self, state: UnifiedFitnessState) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理训练计划生成"""
        try:
            yield {"type": "training_plan_start", "message": "开始生成个性化训练计划..."}
            
            # 调用训练计划服务
            from ...training_plan_service import TrainingPlanService
            plan_service = TrainingPlanService(None)  # 依赖注入待优化
            
            # 流式生成训练计划
            async for plan_chunk in plan_service.generate_training_plan_stream(
                state["user_profile"],
                state["training_params"]
            ):
                yield {
                    "type": "training_plan_chunk",
                    "content": plan_chunk["content"],
                    "chunk_type": plan_chunk.get("chunk_type", "text"),
                    "metadata": plan_chunk.get("metadata", {})
                }
                
                # 流式延迟
                if self.enable_streaming:
                    await asyncio.sleep(self.stream_delay)
            
            yield {"type": "training_plan_complete", "message": "训练计划生成完成！"}
            
        except Exception as e:
            logger.error(f"训练计划流式处理失败: {str(e)}")
            yield {
                "type": "training_plan_error",
                "error_message": "训练计划生成失败，请稍后重试"
            }
    
    async def _process_exercise_recommendation_stream(self, state: UnifiedFitnessState) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理运动推荐"""
        try:
            yield {"type": "exercise_recommendation_start", "message": "正在搜索适合的运动..."}
            
            # 调用运动推荐服务
            from ...exercise_search_service import ExerciseSearchService
            exercise_service = ExerciseSearchService(None)  # 依赖注入待优化
            
            # 流式推荐运动
            async for exercise_chunk in exercise_service.recommend_exercises_stream(
                state["user_profile"],
                state["training_params"],
                state["intent_parameters"]
            ):
                yield {
                    "type": "exercise_recommendation_chunk",
                    "content": exercise_chunk["content"],
                    "exercises": exercise_chunk.get("exercises", []),
                    "metadata": exercise_chunk.get("metadata", {})
                }
                
                if self.enable_streaming:
                    await asyncio.sleep(self.stream_delay)
            
            yield {"type": "exercise_recommendation_complete", "message": "运动推荐完成！"}
            
        except Exception as e:
            logger.error(f"运动推荐流式处理失败: {str(e)}")
            yield {
                "type": "exercise_recommendation_error",
                "error_message": "运动推荐失败，请稍后重试"
            }
    
    async def _process_qa_stream(self, state: UnifiedFitnessState) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理问答"""
        try:
            yield {"type": "qa_start", "message": "正在查找相关信息..."}
            
            # 调用知识库问答服务
            from ...knowledge_service import KnowledgeService
            knowledge_service = KnowledgeService(None)  # 依赖注入待优化
            
            # 流式问答
            latest_message = state["messages"][-1].content if state["messages"] else ""
            async for qa_chunk in knowledge_service.answer_question_stream(
                latest_message,
                state["user_profile"]
            ):
                yield {
                    "type": "qa_chunk",
                    "content": qa_chunk["content"],
                    "sources": qa_chunk.get("sources", []),
                    "confidence": qa_chunk.get("confidence", 0.8)
                }
                
                if self.enable_streaming:
                    await asyncio.sleep(self.stream_delay)
            
            yield {"type": "qa_complete", "message": "问答完成！"}
            
        except Exception as e:
            logger.error(f"问答流式处理失败: {str(e)}")
            yield {
                "type": "qa_error",
                "error_message": "问答处理失败，请稍后重试"
            }
    
    async def _process_general_chat_stream(self, state: UnifiedFitnessState) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理通用聊天"""
        try:
            yield {"type": "chat_start", "message": "正在思考回复..."}
            
            # 调用LLM服务进行通用聊天
            from ...llm_proxy_service import LLMProxyService
            llm_service = LLMProxyService()
            
            # 构建聊天上下文
            chat_context = self._build_chat_context(state)
            
            # 流式聊天
            async for chat_chunk in llm_service.chat_stream(chat_context):
                yield {
                    "type": "chat_chunk",
                    "content": chat_chunk["content"],
                    "finish_reason": chat_chunk.get("finish_reason")
                }
                
                if self.enable_streaming:
                    await asyncio.sleep(self.stream_delay)
            
            yield {"type": "chat_complete", "message": "回复完成！"}
            
        except Exception as e:
            logger.error(f"通用聊天流式处理失败: {str(e)}")
            yield {
                "type": "chat_error",
                "error_message": "聊天处理失败，请稍后重试"
            }
    
    def _build_chat_context(self, state: UnifiedFitnessState) -> Dict[str, Any]:
        """构建聊天上下文"""
        return {
            "messages": state["messages"],
            "user_profile": state["user_profile"],
            "conversation_id": state["conversation_id"],
            "intent": state["intent"]
        }
    
    async def _needs_parameter_collection(self, state: UnifiedFitnessState) -> bool:
        """判断是否需要参数收集"""
        # 如果是训练计划相关意图，需要检查参数完整性
        if state["intent"] in ["create_training_plan", "modify_training_plan"]:
            return (await self._needs_user_info_collection(state) or 
                   await self._needs_training_params_collection(state))
        return False
    
    async def _needs_user_info_collection(self, state: UnifiedFitnessState) -> bool:
        """判断是否需要收集用户信息"""
        required_fields = ["name", "age", "gender", "height", "weight"]
        user_profile = state["user_profile"]
        return any(field not in user_profile or not user_profile[field] for field in required_fields)
    
    async def _needs_training_params_collection(self, state: UnifiedFitnessState) -> bool:
        """判断是否需要收集训练参数"""
        required_params = ["goal", "duration", "frequency"]
        training_params = state["training_params"]
        return any(param not in training_params or not training_params[param] for param in required_params)
    
    async def _update_performance_metrics(self, conversation_id: str, processing_time: float, state: UnifiedFitnessState):
        """更新性能指标"""
        try:
            self.performance_metrics[conversation_id] = {
                "processing_time": processing_time,
                "intent": state["intent"],
                "message_count": len(state["messages"]),
                "error_count": state.get("error_count", 0),
                "timestamp": time.time()
            }
            
            # 异步记录到数据库或监控系统
            # TODO: 实现性能指标持久化
            
        except Exception as e:
            logger.error(f"性能指标更新失败: {str(e)}")
```

#### 3.1.3 验收标准
- [ ] 流式响应延迟 <200ms
- [ ] 并发流式处理能力 >500用户
- [ ] WebSocket连接稳定率 >99%
- [ ] 流式处理错误恢复正常

### 3.2 Day 25-28: 流式处理测试和优化

#### 3.2.1 测试清单
- [ ] 流式响应延迟测试
- [ ] 并发流式处理测试
- [ ] WebSocket连接稳定性测试
- [ ] 流式处理性能优化
- [ ] 端到端流式处理测试

## 4. 阶段二总结和交付

### 4.1 关键交付物
1. **增强参数管理器** - 完整的参数收集和验证流程
2. **增强LangGraph服务** - 统一的流式处理系统
3. **流式处理管理器** - WebSocket和流式传输控制
4. **完整测试套件** - 参数收集和流式处理测试
5. **性能优化报告** - 流式处理性能调优建议

### 4.2 最终验收标准

#### 4.2.1 功能验收
- [ ] 参数收集流程完整可用
- [ ] 流式处理系统稳定运行
- [ ] WebSocket连接管理正常
- [ ] 错误处理和恢复机制完善

#### 4.2.2 性能验收
- [ ] 参数收集成功率 >98%
- [ ] 流式响应首字节时间 <200ms
- [ ] 并发流式处理 >500 QPS
- [ ] WebSocket连接稳定率 >99%

#### 4.2.3 质量验收
- [ ] 代码覆盖率 >90%
- [ ] 性能测试通过
- [ ] 压力测试通过
- [ ] 用户体验测试通过

### 4.3 下一阶段准备
- [ ] 阶段三环境准备
- [ ] 错误处理模块依赖检查
- [ ] 缓存系统环境配置
- [ ] 监控系统集成准备 